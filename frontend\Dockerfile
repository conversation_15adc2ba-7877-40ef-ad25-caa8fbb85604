# Multi-stage build for frontend
FROM node:20-alpine AS build
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache git python3 make g++ libc6-compat

# Set npm configuration
RUN npm config set fetch-retry-maxtimeout 600000
RUN npm config set fetch-retry-mintimeout 10000
RUN npm config set fetch-timeout 600000

# Copy package files
COPY package.json ./

# Install core dependencies first
RUN npm install react react-dom --no-optional --legacy-peer-deps
RUN npm install vite @vitejs/plugin-react --no-optional --legacy-peer-deps

# Install remaining dependencies
RUN npm install --no-optional --legacy-peer-deps || npm install --force
RUN npm install @rollup/rollup-linux-x64-gnu --save-optional || true

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]