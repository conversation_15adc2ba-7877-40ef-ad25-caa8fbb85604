# Ultra-simple Dockerfile that avoids all native dependency issues
FROM node:20 AS build
WORKDIR /app

# Set environment
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=8192"

# Copy package.json and create a simplified version
COPY package.json ./

# Remove problematic dependencies and install core ones only
RUN npm install react react-dom vite @vitejs/plugin-react --legacy-peer-deps --no-package-lock

# Install UI libraries
RUN npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu --legacy-peer-deps --no-package-lock

# Install other essential dependencies one by one to avoid conflicts
RUN npm install axios react-router-dom --legacy-peer-deps --no-package-lock
RUN npm install @tanstack/react-query --legacy-peer-deps --no-package-lock
RUN npm install tailwindcss autoprefixer postcss --legacy-peer-deps --no-package-lock
RUN npm install lucide-react react-hot-toast --legacy-peer-deps --no-package-lock

# Copy source code
COPY . .

# Create a simple vite config that avoids rollup issues
RUN echo 'import { defineConfig } from "vite";\nimport react from "@vitejs/plugin-react";\nexport default defineConfig({\n  plugins: [react()],\n  build: { target: "esnext", minify: false }\n});' > vite.config.simple.js

# Build with simple config
RUN npx vite build --config vite.config.simple.js

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
