# Alternative Dockerfile using Ubuntu base (more compatible)
FROM node:20 AS build
WORKDIR /app

# Set memory limits and environment
ENV NODE_OPTIONS="--max-old-space-size=8192"
ENV NODE_ENV=production

# Update package lists
RUN apt-get update && apt-get install -y python3 make g++

# Copy package files
COPY package.json ./
COPY vite.config.js ./

# Install dependencies with memory optimization
RUN npm install --no-optional --legacy-peer-deps --max-old-space-size=8192

# Copy source code
COPY . .

# Build with memory optimization and single-threaded esbuild
RUN NODE_OPTIONS="--max-old-space-size=8192" npm run build

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
