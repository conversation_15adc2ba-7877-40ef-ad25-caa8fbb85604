# Multi-stage build for frontend
FROM node:18-alpine AS build
WORKDIR /app

# Install git (needed for some npm packages)
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Clear npm cache and install dependencies
RUN npm cache clean --force
RUN npm install --no-optional --legacy-peer-deps
RUN npm install @rollup/rollup-linux-x64-gnu --save-optional || true

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
