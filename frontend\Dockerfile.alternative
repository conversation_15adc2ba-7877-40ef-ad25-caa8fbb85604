# Alternative Dockerfile using Ubuntu base (more compatible)
FROM node:20 AS build
WORKDIR /app

# Update package lists
RUN apt-get update

# Copy package files
COPY package.json ./

# Install dependencies with verbose logging
RUN npm install --verbose --no-optional --legacy-peer-deps

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
