# Docker Setup Guide for UpKeepPro

This guide explains how to run UpKeepPro using Docker with different configurations for development and production.

## 🏗️ Architecture Overview

### Production Setup (docker-compose.yml)
- **Frontend**: React app served by <PERSON>inx (Port 3000)
- **Backend**: Express.js API server (Port 5000)
- **Database**: Connects to your local MongoDB (accessible via MongoDB Compass)

### Development Setup (docker-compose.dev.yml)
- **Frontend**: React app served by <PERSON>in<PERSON> (Port 3001)
- **Backend**: Express.js API server (Port 5001)
- **Database**: Containerized MongoDB (Port 27018)

## 🚀 Quick Start

### Production Mode (Connect to MongoDB Compass)
```bash
# Make sure your local MongoDB is running and accessible via MongoDB Compass
# Update .env.production with your actual Cloudinary credentials

# Run production containers
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
```

### Development Mode (Isolated containers)
```bash
# Run development containers with isolated MongoDB
docker-compose -f docker-compose.dev.yml up -d

# Access the application
# Frontend: http://localhost:3001
# Backend API: http://localhost:5001
# MongoDB: localhost:27018 (for external access)
```

## 📋 Prerequisites

### For Production Mode
1. **Local MongoDB**: Must be running on your machine
2. **MongoDB Compass**: Should be able to connect to `mongodb://localhost:27017`
3. **Data**: Your existing UpKeepPro database should be accessible

### For Development Mode
1. **Docker & Docker Compose**: Installed and running
2. **Ports Available**: 3001, 5001, 27018

## 🔧 Configuration Files

### Environment Files
- `.env` - Your current local development environment
- `.env.production` - Production environment (connects to local MongoDB)
- `.env.dev` - Development environment (connects to containerized MongoDB)

### Docker Compose Files
- `docker-compose.yml` - Production setup (connects to local MongoDB)
- `docker-compose.dev.yml` - Development setup (isolated containers)

## 🗄️ Database Connections

### Production (docker-compose.yml)
```
mongodb://host.docker.internal:27017/UpKeepPro
```
- Connects to your local MongoDB
- Data visible in MongoDB Compass
- Uses existing UpKeepPro database

### Development (docker-compose.dev.yml)
```
********************************************
```
- Uses containerized MongoDB
- Isolated from your production data
- Fresh database for testing

## 🛠️ Common Commands

### Production
```bash
# Start production containers
docker-compose up -d

# View logs
docker-compose logs -f

# Stop containers
docker-compose down

# Rebuild and start
docker-compose up --build -d
```

### Development
```bash
# Start development containers
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop containers
docker-compose -f docker-compose.dev.yml down

# Rebuild and start
docker-compose -f docker-compose.dev.yml up --build -d
```

## 🔍 Troubleshooting

### Production Issues
1. **Can't connect to MongoDB**: Ensure local MongoDB is running on port 27017
2. **Permission denied**: Make sure Docker has access to host network
3. **Data not found**: Verify your UpKeepPro database exists locally

### Development Issues
1. **Port conflicts**: Make sure ports 3001, 5001, 27018 are available
2. **Container startup**: Check if Docker daemon is running
3. **Build failures**: Try `docker system prune` to clean up

## 📝 Notes

- **Production**: Uses your existing MongoDB data (safe for production)
- **Development**: Creates isolated environment (safe for testing)
- **Ports**: Different ports prevent conflicts between environments
- **Data**: Production preserves your data, development starts fresh

## 🔐 Security Considerations

- Update JWT_SECRET in production
- Add your actual Cloudinary credentials
- Consider using Docker secrets for sensitive data
- Ensure MongoDB is properly secured in production
