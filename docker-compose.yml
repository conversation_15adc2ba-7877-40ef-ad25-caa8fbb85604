version: "3.8"
services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: backend
    restart: always
    ports:
      - "5000:5000"
    env_file:
      - .env
    environment:
      # Connect to host MongoDB (accessible via MongoDB Compass)
      MONGO_URI: mongodb://host.docker.internal:27017/UpKeepPro
      # Connect to host Ollama service
      OLLAMA_API_URL: http://host.docker.internal:11434/api
      NODE_ENV: production
      PORT: ${PORT}
      JWT_SECRET: ${JWT_SECRET}

    extra_hosts:
      - "host.docker.internal:host-gateway"

  frontend:
    build: ./frontend
    container_name: frontend
    restart: always
    ports:
      - "3000:80"
    depends_on:
      - backend
