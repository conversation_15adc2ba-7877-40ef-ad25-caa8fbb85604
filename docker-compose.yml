version: "3.8"
services:
  mongo:
    image: mongo:7
    container_name: mongo
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
    volumes:
      - mongo-data:/data/db

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: backend
    restart: always
    ports:
      - "5000:5000"
    env_file:
      - .env
    environment:
      MONGO_URI: ${MONGO_URI}
      NODE_ENV: production
      PORT: ${PORT}
      JWT_SECRET: ${JWT_SECRET}
      CLOUDINARY_CLOUD_NAME: ${CLOUDINARY_CLOUD_NAME}
      CLOUDINARY_API_KEY: ${CLOUDINARY_API_KEY}
      CLOUDINARY_API_SECRET: ${CLOUDINARY_API_SECRET}
    depends_on:
      - mongo

  frontend:
    build: ./frontend
    container_name: frontend
    restart: always
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  mongo-data:
