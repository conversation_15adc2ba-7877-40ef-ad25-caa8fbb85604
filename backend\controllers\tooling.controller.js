import Tooling from "../models/tooling.modal.js";
import mongoose from "mongoose";
import { getOrCreateMAT } from "../utils/matGenerator.js";
// @desc    Acquire new tooling (via PV or M11)
// @route   POST /api/tooling/acquire

export const acquireTooling = async (req, res) => {
  try {
    const {
      designation,
      acquisitionType,
      acquisitionRef,
      acquisitionDate,
      originalQte,
      responsible,
      location,
      placement,
      type,
      direction,
      notes,
    } = req.body;

    const requiredFields = [
      "designation",
      "acquisitionType",
      "acquisitionRef",
      "acquisitionDate",
      "originalQte",
      // REMOVE these lines:
      // "responsible",
      // "location",
      // "placement",
      "type",
      "direction",
    ];

    const missing = requiredFields.filter((field) => !req.body[field]);
    if (missing.length > 0) {
      return res
        .status(400)
        .json({ error: `Missing fields: ${missing.join(", ")}` });
    }

    const existingTool = await Tooling.findOne({ designation });

    if (existingTool) {
      existingTool.originalQte += originalQte;
      existingTool.currentQte += originalQte;
      existingTool.history.push({
        eventType: "entry",
        reference:
          acquisitionType === "PV"
            ? `pv-${acquisitionRef}`
            : `m11-${acquisitionRef}`,
        date: new Date(acquisitionDate),
        qteChange: originalQte,
        notes:
          notes ||
          `Additional quantity for ${acquisitionType} ${acquisitionRef}`,
        performedBy: req.user?.id || "system",
        responsible: responsible === "" ? undefined : responsible,
        placement: placement === "" ? undefined : placement,
        direction,
        location: location === "" ? undefined : location,
        type,
        acquisitionType,
        acquisitionRef,
        acquisitionDate,
      });
      await existingTool.save();
      return res.status(200).json(existingTool);
    }

    // Generate MAT only for new tools
    const mat = await getOrCreateMAT(designation);

    const cleanedResponsible = responsible === "" ? undefined : responsible;
    const cleanedLocation = location === "" ? undefined : location;
    const cleanedPlacement = placement === "" ? undefined : placement;

    const newTool = new Tooling({
      designation,
      mat,
      acquisitionType,
      acquisitionRef,
      acquisitionDate,
      originalQte,
      currentQte: originalQte,
      responsible: cleanedResponsible,
      location: cleanedLocation,
      placement: cleanedPlacement,
      type,
      direction,
      situation: "available",
      notes,
      history: [
        {
          eventType: "entry",
          reference:
            acquisitionType === "PV"
              ? `pv-${acquisitionRef}`
              : `m11-${acquisitionRef}`,
          date: new Date(acquisitionDate),
          qteChange: originalQte,
          notes:
            notes ||
            `Initial ${acquisitionType} - ${acquisitionRef} acquisition`,
          performedBy: req.user?.id || "system",
          responsible: cleanedResponsible,
          placement: cleanedPlacement,
          direction,
          location: cleanedLocation,
          type,
          acquisitionType,
          acquisitionRef,
          acquisitionDate,
        },
      ],
    });

    const saved = await newTool.save();
    res.status(201).json(saved);
  } catch (err) {
    if (err.code === 11000 && err.keyPattern?.mat) {
      return res
        .status(409)
        .json({ error: "Duplicate MAT detected. Try again." });
    }

    res.status(500).json({ error: err.message });
  }
};

// @desc    Exit tooling (M11 or C12)
// @route   POST /api/tooling/:id/exit
export const exitTooling = async (req, res) => {
  try {
    const { id } = req.params;
    const { exitRef, exitDate, exitQte, exitReason, notes } = req.body;

    // Validate required fields
    if (!exitRef || !exitDate || !exitQte || !exitReason) {
      return res.status(400).json({
        error: "Exit reference, date, quantity and reason are required",
      });
    }

    if (exitQte <= 0) {
      return res.status(400).json({
        error: "Exit quantity must be greater than 0",
      });
    }

    // Find the tool
    const tool = await Tooling.findById(id);
    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // Check available quantity
    if (exitQte > tool.currentQte) {
      return res.status(400).json({
        error: `Not enough stock. Available: ${tool.currentQte}`,
      });
    }

    // Add exit record
    tool.exits.push({
      exitRef,
      exitDate: new Date(exitDate),
      exitQte,
      exitReason,
    });

    // Update current quantity
    tool.currentQte -= exitQte;

    // Update situation
    if (tool.currentQte <= 0) {
      tool.situation = "unavailable";
    } else if (tool.currentQte < tool.originalQte) {
      tool.situation = "partial";
    }

    // Add to history
    tool.history.push({
      eventType: "exit",
      reference: exitRef,
      date: new Date(exitDate),
      qteChange: -exitQte,
      notes,
      performedBy: req.user?.id || "system",
    });

    await tool.save();
    res.status(200).json(tool);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Convert PV acquisition to M11
// @route   POST /api/tooling/:id/convert
export const convertPVtoM11 = async (req, res) => {
  try {
    const { id } = req.params;
    const { m11Ref, m11Date, notes, pvReference } = req.body;

    if (!m11Ref || !m11Date) {
      return res.status(400).json({
        error: "M11 reference and date are required",
      });
    }

    const tool = await Tooling.findById(id);
    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // If converting a specific PV entry
    if (pvReference) {
      // Find the specific entry in history
      const entryIndex = tool.history.findIndex(
        (entry) =>
          entry.eventType === "entry" && entry.reference === pvReference
      );

      if (entryIndex === -1) {
        return res
          .status(404)
          .json({ error: "PV reference not found in tool history" });
      }

      // Update only this specific entry
      tool.history[entryIndex].reference = `m11-${m11Ref}`;

      // Update notes if they reference PV
      if (
        tool.history[entryIndex].notes &&
        tool.history[entryIndex].notes.includes("PV")
      ) {
        tool.history[entryIndex].notes = tool.history[entryIndex].notes.replace(
          "PV",
          "M11"
        );
      }

      // Add conversion event to history
      tool.history.push({
        eventType: "conversion",
        reference: m11Ref,
        date: new Date(m11Date),
        notes: notes || `Converted ${pvReference} to M11-${m11Ref}`,
        performedBy: req.user?.id || "system",
      });

      // Check if this was the main acquisition reference
      if (
        tool.acquisitionType === "PV" &&
        pvReference === `pv-${tool.acquisitionRef}`
      ) {
        // Update main acquisition info
        tool.acquisitionType = "M11";
        tool.acquisitionRef = m11Ref;
        tool.acquisitionDate = new Date(m11Date);
      }
    }
    // If converting the entire tool (legacy behavior)
    else if (tool.acquisitionType === "PV") {
      // Update main acquisition info
      tool.acquisitionType = "M11";
      tool.acquisitionRef = m11Ref;
      tool.acquisitionDate = new Date(m11Date);

      // Update all PV references in history entries to M11 references
      tool.history.forEach((entry) => {
        if (entry.eventType === "entry" && entry.reference.startsWith("pv-")) {
          // Update to M11 format
          entry.reference = `m11-${m11Ref}`;
          // Update notes if they reference PV
          if (entry.notes && entry.notes.includes("PV")) {
            entry.notes = entry.notes.replace("PV", "M11");
          }
        }
      });

      // Add conversion event to history
      tool.history.push({
        eventType: "conversion",
        reference: m11Ref,
        date: new Date(m11Date),
        notes: notes || `Converted all PV entries to M11-${m11Ref}`,
        performedBy: req.user?.id || "system",
      });
    } else {
      return res.status(400).json({
        error: "Only PV acquisitions can be converted",
      });
    }

    await tool.save();
    res.status(200).json(tool);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Get all tooling records with filters
// @route   GET /api/tooling
export const getAllTooling = async (req, res) => {
  try {
    const { direction, type, situation } = req.query;
    const filter = {};

    if (direction) filter.direction = direction;
    if (type) filter.type = type;
    if (situation) filter.situation = situation;

    const tools = await Tooling.find(filter)
      .populate("responsible", "name")
      .populate("location", "name")
      .populate("placement", "name")
      .sort({ designation: 1 });

    res.status(200).json(tools);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Get tooling by ID with full details
// @route   GET /api/tooling/:id
export const getToolingById = async (req, res) => {
  try {
    const tool = await Tooling.findById(req.params.id)
      .populate("responsible", "name position")
      .populate("location", "name code")
      .populate("placement", "name section");

    if (!tool) {
      return res.status(404).json({ message: "Tool not found" });
    }

    res.status(200).json(tool);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Get current stock level
// @route   GET /api/tooling/:id/stock
export const getToolStock = async (req, res) => {
  try {
    const tool = await Tooling.findById(req.params.id).select(
      "designation mat originalQte currentQte situation"
    );

    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    res.status(200).json({
      designation: tool.designation,
      mat: tool.mat,
      originalQuantity: tool.originalQte,
      currentQuantity: tool.currentQte,
      situation: tool.situation,
      exitsCount: tool.exits.length,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Get full history of a tool
// @route   GET /api/tooling/:id/history
export const getToolHistory = async (req, res) => {
  try {
    const tool = await Tooling.findById(req.params.id)
      .populate({
        path: "history.responsible",
        select: "name position",
      })
      .populate({
        path: "history.location",
        select: "name code",
      })
      .populate({
        path: "history.placement",
        select: "name section",
      })
      .select("history exits designation mat direction type") // Include main tool direction and type
      .lean();

    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // Combine history entries and exit records
    const combinedHistory = [];

    // Add all history entries, ensuring responsible/location/placement are populated
    (tool.history || []).forEach((h) => {
      combinedHistory.push({
        ...h,
        responsible: h.responsible || null,
        location: h.location || null,
        placement: h.placement || null,
        // Ensure direction and type are present, falling back to tool's main if not in history entry
        direction: h.direction || tool.direction || null,
        type: h.type || tool.type || null,
      });
    });

    // Add exit records, ensuring they are not duplicates of existing history entries
    (tool.exits || []).forEach((exit) => {
      // Check if an equivalent exit record already exists in history
      const isDuplicate = combinedHistory.some(
        (h) => h.eventType === "exit" && h.reference === exit.exitRef
      );

      if (!isDuplicate) {
        combinedHistory.push({
          _id: exit._id,
          eventType: "exit",
          reference: exit.exitRef,
          date: exit.exitDate,
          qteChange: -exit.exitQte,
          notes: exit.exitReason,
          performedBy: "system", // Or actual user if tracked for exits
          responsible: null, // Exits don't have these fields directly
          location: null,
          placement: null,
          direction: tool.direction || null, // Use tool's main direction
          type: tool.type || null, // Use tool's main type
        });
      }
    });

    // Sort combined history by date, newest first
    combinedHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

    res.json(combinedHistory);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Update tooling details
// @route   PUT /api/tooling/:id
export const updateTooling = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      designation,
      responsible,
      location,
      placement,
      type,
      direction,
      notes,
    } = req.body;

    const updates = {};
    if (designation) updates.designation = designation;
    if (responsible)
      updates.responsible = new mongoose.Types.ObjectId(responsible);
    if (location) updates.location = new mongoose.Types.ObjectId(location);
    if (placement) updates.placement = new mongoose.Types.ObjectId(placement);
    if (type) updates.type = type;
    if (direction) updates.direction = direction;

    const updatedTool = await Tooling.findByIdAndUpdate(id, updates, {
      new: true,
      runValidators: true,
    });

    if (!updatedTool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // Add to history if any updates
    if (Object.keys(updates).length > 0) {
      updatedTool.history.push({
        eventType: "adjustment",
        date: new Date(),
        notes: notes || "General information update",
        performedBy: req.user?.id || "system",
      });
      await updatedTool.save();
    }

    res.status(200).json(updatedTool);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Delete tooling (with history preservation)
// @route   DELETE /api/tooling/:id
export const deleteTooling = async (req, res) => {
  try {
    const tool = await Tooling.findByIdAndDelete(req.params.id);

    if (!tool) {
      return res.status(404).json({ message: "Tool not found" });
    }

    // In production, you might want to archive instead of delete
    res.status(200).json({
      message: "Tool deleted successfully",
      deletedTool: tool.designation,
      mat: tool.mat,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Additional utility controllers
export const getToolsByDirection = async (req, res) => {
  try {
    const tools = await Tooling.find({ direction: req.params.direction })
      .select("designation mat currentQte situation")
      .sort({ designation: 1 });
    res.status(200).json(tools);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const getToolsByType = async (req, res) => {
  try {
    const { type } = req.params;
    const tools = await Tooling.find({ type: type }).populate(
      "responsible location placement"
    );
    res.status(200).json(tools);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// @desc    Delete a specific history entry from a tool
// @route   DELETE /api/tooling/:toolId/history/:entryId
export const deleteToolHistoryEntry = async (req, res) => {
  try {
    const { toolId, entryId } = req.params;

    if (
      !mongoose.Types.ObjectId.isValid(toolId) ||
      !mongoose.Types.ObjectId.isValid(entryId)
    ) {
      return res.status(400).json({ error: "Invalid Tool ID or Entry ID" });
    }

    const tool = await Tooling.findById(toolId);

    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    const entryIndex = tool.history.findIndex(
      (entry) => entry._id.toString() === entryId
    );

    if (entryIndex === -1) {
      return res.status(404).json({ error: "History entry not found" });
    }

    const deletedEntry = tool.history.splice(entryIndex, 1)[0];

    // Adjust currentQte based on the deleted entry's impact
    if (deletedEntry.qteChange) {
      if (deletedEntry.eventType === "entry") {
        tool.currentQte -= deletedEntry.qteChange;
      } else if (deletedEntry.eventType === "exit") {
        tool.currentQte -= deletedEntry.qteChange; // qteChange for exit is negative, so subtracting a negative adds it back
      }
    }

    // Recalculate situation based on updated currentQte
    if (tool.currentQte <= 0) {
      tool.situation = "unavailable";
    } else if (tool.currentQte < tool.originalQte) {
      tool.situation = "partial";
    } else {
      tool.situation = "available"; // If currentQte is back to original or more (shouldn't be more, but just in case)
    }

    await tool.save();

    res
      .status(200)
      .json({ message: "History entry deleted successfully", tool });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
