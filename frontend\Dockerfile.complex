# Simple Dockerfile avoiding native dependency issues
FROM node:20 AS build
WORKDIR /app

# Set memory limits and environment
ENV NODE_OPTIONS="--max-old-space-size=8192"
ENV NODE_ENV=production

# Copy package files and remove package-lock to avoid conflicts
COPY package.json ./

# Clean install without optional dependencies and native binaries
RUN npm install --omit=optional --legacy-peer-deps --no-package-lock

# Install specific rollup binary for Linux
RUN npm install @rollup/rollup-linux-x64-gnu --save-optional || \
    npm install @rollup/rollup-linux-x64-musl --save-optional || \
    echo "Rollup binary install failed, continuing..."

# Copy source code
COPY . .

# Build with fallback to JS-only rollup if native fails
RUN npm run build || (npm uninstall rollup && npm install rollup@3.29.4 && npm run build)

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
