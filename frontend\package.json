{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "check-imports": "node check-imports.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@syncfusion/ej2": "^19.4.48", "@syncfusion/ej2-react-calendars": "^19.4.48", "@syncfusion/ej2-react-charts": "^19.4.50", "@syncfusion/ej2-react-dropdowns": "^19.4.52", "@syncfusion/ej2-react-grids": "^19.4.50", "@syncfusion/ej2-react-inputs": "^19.4.52", "@syncfusion/ej2-react-kanban": "^19.4.48", "@syncfusion/ej2-react-popups": "^19.4.52", "@syncfusion/ej2-react-richtexteditor": "^19.4.50", "@syncfusion/ej2-react-schedule": "^19.4.50", "@tanstack/react-query": "^5.66.0", "@tanstack/react-table": "^8.21.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "docx": "^9.5.0", "framer-motion": "^12.12.2", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "moment": "^2.30.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-calendar": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-router-dom": "^7.1.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "daisyui": "^4.12.23", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "postcss-nested": "^7.0.2", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}