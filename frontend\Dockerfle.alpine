# Multi-stage build for frontend
FROM node:20-alpine AS build
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache git python3 make g++ libc6-compat

# Increase Node.js memory limit and set environment variables
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NODE_ENV=production

# Set npm configuration
RUN npm config set fetch-retry-maxtimeout 600000
RUN npm config set fetch-retry-mintimeout 10000
RUN npm config set fetch-timeout 600000

# Copy package files
COPY package.json ./

# Install dependencies with increased memory
RUN npm install --no-optional --legacy-peer-deps --max-old-space-size=4096
RUN npm install @rollup/rollup-linux-x64-gnu --save-optional || true

# Copy source code
COPY . .

# Build with increased memory and esbuild optimizations
ENV ESBUILD_BINARY_PATH=/app/node_modules/esbuild/bin/esbuild
RUN npm run build -- --mode production

# Production image
FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]