# Development Environment Configuration
# MongoDB Configuration - Connect to containerized MongoDB
MONGO_URI=********************************************
PORT=5000

# JWT Configuration
JWT_SECRET=ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=

# Environment
NODE_ENV=development
FRONTEND_URL=http://localhost:3001

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
