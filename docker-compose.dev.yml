version: "3.8"
services:
  mongo:
    image: mongo:7
    container_name: mongo-dev
    restart: always
    ports:
      - "27018:27017"  # Different port to avoid conflict with local MongoDB
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
    volumes:
      - mongo-dev-data:/data/db

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: backend-dev
    restart: always
    ports:
      - "5001:5000"  # Different port for development
    env_file:
      - .env.dev
    environment:
      # Connect to containerized MongoDB for development
      MONGO_URI: ********************************************
      NODE_ENV: development
      PORT: 5000
      JWT_SECRET: ${JWT_SECRET}
      CLOUDINARY_CLOUD_NAME: ${CLOUDINARY_CLOUD_NAME}
      CLOUDINARY_API_KEY: ${CLOUDINARY_API_KEY}
      CLOUDINARY_API_SECRET: ${CLOUDINARY_API_SECRET}
    depends_on:
      - mongo

  frontend:
    build: ./frontend
    container_name: frontend-dev
    restart: always
    ports:
      - "3001:80"  # Different port for development
    depends_on:
      - backend

volumes:
  mongo-dev-data:
