#!/usr/bin/env node

/**
 * Import Path Checker for Docker Build Compatibility
 * 
 * This script checks for common import path issues that can cause
 * Docker builds to fail, especially case sensitivity problems.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, 'src');
const issues = [];

// Function to recursively find all .jsx and .js files
function findJSFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules') {
      files.push(...findJSFiles(fullPath));
    } else if (item.endsWith('.jsx') || item.endsWith('.js')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to check import statements in a file
function checkImports(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const importMatch = line.match(/import.*from\s+['"](\.\.?\/[^'"]+)['"]/);
    if (importMatch) {
      const importPath = importMatch[1];
      const resolvedPath = path.resolve(path.dirname(filePath), importPath);
      
      // Check if the imported file exists
      const possibleExtensions = ['', '.js', '.jsx', '.ts', '.tsx'];
      let exists = false;
      
      for (const ext of possibleExtensions) {
        if (fs.existsSync(resolvedPath + ext)) {
          exists = true;
          break;
        }
      }
      
      // Check if it's a directory with index file
      if (!exists && fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory()) {
        for (const ext of possibleExtensions) {
          if (fs.existsSync(path.join(resolvedPath, 'index' + ext))) {
            exists = true;
            break;
          }
        }
      }
      
      if (!exists) {
        issues.push({
          file: path.relative(srcDir, filePath),
          line: index + 1,
          import: importPath,
          issue: 'File not found'
        });
      }
    }
  });
}

// Main execution
console.log('🔍 Checking import paths for Docker build compatibility...\n');

try {
  const jsFiles = findJSFiles(srcDir);
  
  for (const file of jsFiles) {
    checkImports(file);
  }
  
  if (issues.length === 0) {
    console.log('✅ All import paths look good!');
  } else {
    console.log('❌ Found potential issues:');
    console.log('=====================================\n');
    
    issues.forEach(issue => {
      console.log(`📁 File: ${issue.file}`);
      console.log(`📍 Line: ${issue.line}`);
      console.log(`📦 Import: ${issue.import}`);
      console.log(`⚠️  Issue: ${issue.issue}`);
      console.log('---');
    });
    
    console.log(`\n❌ Found ${issues.length} import issues that may cause Docker build failures.`);
    console.log('Please fix these before building Docker containers.');
    process.exit(1);
  }
} catch (error) {
  console.error('Error checking imports:', error);
  process.exit(1);
}
