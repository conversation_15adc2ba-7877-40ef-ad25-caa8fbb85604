# Production Environment Configuration
# MongoDB Configuration - Connect to local MongoDB (MongoDB Compass)
MONGO_URI=mongodb://host.docker.internal:27017/UpKeepPro
PORT=5000

# JWT Configuration
JWT_SECRET=ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=

# Environment
NODE_ENV=production
FRONTEND_URL=http://localhost:3000

# Cloudinary Configuration (Add your production values)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
