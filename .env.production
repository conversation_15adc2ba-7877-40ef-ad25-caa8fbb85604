# Production Environment Configuration
# MongoDB Configuration - Connect to local MongoDB (MongoDB Compass)
MONGO_URI=mongodb://host.docker.internal:27017/UpKeepPro
PORT=5000

# Ollama Configuration - Connect to local Ollama service
OLLAMA_API_URL=http://host.docker.internal:11434/api

# JWT Configuration
JWT_SECRET=ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=

# Environment
NODE_ENV=production
FRONTEND_URL=http://localhost:3000

